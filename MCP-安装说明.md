# MCP Feedback Enhanced 安装完成

## ✅ 安装状态
- **Python 3.12.8**: ✅ 已安装 (使用 pyenv)
- **MCP Feedback Enhanced v2.6.0**: ✅ 已安装
- **Web UI 测试**: ✅ 运行正常 (http://127.0.0.1:9765)

## 📁 配置文件
已为您创建了两个配置文件：

### 1. Web UI 模式 (推荐)
文件：`mcp-config.json`
- 使用浏览器界面
- 适合大多数使用场景

### 2. 桌面应用模式
文件：`mcp-config-desktop.json`
- 使用原生桌面应用
- 跨平台支持

## 🔧 配置步骤（根据您的工具选择）

### 如果使用 **Cursor**
1. 打开 Cursor 设置
2. 找到 MCP 服务器配置
3. 添加以下配置：
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "/Users/<USER>/.pyenv/versions/3.12.8/bin/python",
      "args": ["-m", "mcp_feedback_enhanced"],
      "timeout": 600,
      "env": {
        "MCP_DEBUG": "false",
        "MCP_WEB_HOST": "127.0.0.1",
        "MCP_WEB_PORT": "9765",
        "MCP_LANGUAGE": "zh-CN"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

### 如果使用 **VS Code + Cline 扩展**
1. 打开 VS Code 设置 (JSON)
2. 添加以下配置：
```json
{
  "cline.mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "/Users/<USER>/.pyenv/versions/3.12.8/bin/python",
      "args": ["-m", "mcp_feedback_enhanced"],
      "timeout": 600,
      "env": {
        "MCP_DEBUG": "false",
        "MCP_WEB_HOST": "127.0.0.1",
        "MCP_WEB_PORT": "9765",
        "MCP_LANGUAGE": "zh-CN"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

### 如果使用 **Claude Desktop**
1. 编辑配置文件：`~/Library/Application Support/Claude/claude_desktop_config.json`
2. 添加上述 mcpServers 配置

### 如果使用 **Windsurf**
1. 编辑配置文件：`~/.windsurf/mcp_servers.json`
2. 添加上述 mcpServers 配置

### 重启应用
完全关闭并重新打开您的 AI 编程工具

## 🎯 使用方法

### 在 AI 助手中添加规则
在您的 AI 助手设置中添加以下规则：
```
# MCP Interactive Feedback Rules
follow mcp-feedback-enhanced instructions
```

### 测试功能
1. 让 AI 助手执行任何需要确认的操作
2. MCP 工具会自动启动 Web UI 或桌面应用
3. 在界面中提供反馈
4. AI 助手会根据您的反馈继续工作

## 🌐 Web UI 功能
- **智能提示管理**: 保存常用回复
- **自动提交**: 设置定时自动提交
- **会话管理**: 跟踪历史记录
- **多语言支持**: 中文/英文界面
- **图片上传**: 支持拖拽和粘贴

## 🔧 环境变量说明
- `MCP_DEBUG`: 调试模式 (true/false)
- `MCP_WEB_HOST`: Web 服务器主机 (127.0.0.1)
- `MCP_WEB_PORT`: Web 服务器端口 (9765)
- `MCP_DESKTOP_MODE`: 桌面应用模式 (true/false)
- `MCP_LANGUAGE`: 界面语言 (zh-CN/zh-TW/en)

## 🚀 快捷键
- `Ctrl+Enter` (Windows/Linux) / `Cmd+Enter` (macOS): 提交反馈
- `Ctrl+V` (Windows/Linux) / `Cmd+V` (macOS): 粘贴图片
- `Ctrl+I` (Windows/Linux) / `Cmd+I` (macOS): 聚焦输入框

## 📞 支持
- GitHub: https://github.com/Minidoracat/mcp-feedback-enhanced
- 问题反馈: https://github.com/Minidoracat/mcp-feedback-enhanced/issues

## 🎉 安装完成！
您现在可以在 VS Code 中使用 MCP Feedback Enhanced 工具了！
