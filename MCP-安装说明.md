# MCP Feedback Enhanced 安装完成

## ✅ 安装状态
- **Python 3.12.8**: ✅ 已安装 (使用 pyenv)
- **MCP Feedback Enhanced v2.6.0**: ✅ 已安装
- **Web UI 测试**: ✅ 运行正常 (http://127.0.0.1:9765)

## 📁 配置文件
已为您创建了两个配置文件：

### 1. Web UI 模式 (推荐)
文件：`mcp-config.json`
- 使用浏览器界面
- 适合大多数使用场景

### 2. 桌面应用模式
文件：`mcp-config-desktop.json`
- 使用原生桌面应用
- 跨平台支持

## 🔧 VS Code 配置步骤

1. **打开 VS Code 设置**
   - 按 `Cmd+Shift+P` 打开命令面板
   - 输入 "Preferences: Open Settings (JSON)"

2. **添加 MCP 配置**
   将以下内容添加到 settings.json 中：
   ```json
   {
     "mcp.servers": {
       "mcp-feedback-enhanced": {
         "command": "/Users/<USER>/.pyenv/versions/3.12.8/bin/python",
         "args": ["-m", "mcp_feedback_enhanced"],
         "timeout": 600,
         "env": {
           "MCP_DEBUG": "false",
           "MCP_WEB_HOST": "127.0.0.1",
           "MCP_WEB_PORT": "9765",
           "MCP_LANGUAGE": "zh-CN"
         },
         "autoApprove": ["interactive_feedback"]
       }
     }
   }
   ```

3. **重启 VS Code**
   完全关闭并重新打开 VS Code

## 🎯 使用方法

### 在 AI 助手中添加规则
在您的 AI 助手设置中添加以下规则：
```
# MCP Interactive Feedback Rules
follow mcp-feedback-enhanced instructions
```

### 测试功能
1. 让 AI 助手执行任何需要确认的操作
2. MCP 工具会自动启动 Web UI 或桌面应用
3. 在界面中提供反馈
4. AI 助手会根据您的反馈继续工作

## 🌐 Web UI 功能
- **智能提示管理**: 保存常用回复
- **自动提交**: 设置定时自动提交
- **会话管理**: 跟踪历史记录
- **多语言支持**: 中文/英文界面
- **图片上传**: 支持拖拽和粘贴

## 🔧 环境变量说明
- `MCP_DEBUG`: 调试模式 (true/false)
- `MCP_WEB_HOST`: Web 服务器主机 (127.0.0.1)
- `MCP_WEB_PORT`: Web 服务器端口 (9765)
- `MCP_DESKTOP_MODE`: 桌面应用模式 (true/false)
- `MCP_LANGUAGE`: 界面语言 (zh-CN/zh-TW/en)

## 🚀 快捷键
- `Ctrl+Enter` (Windows/Linux) / `Cmd+Enter` (macOS): 提交反馈
- `Ctrl+V` (Windows/Linux) / `Cmd+V` (macOS): 粘贴图片
- `Ctrl+I` (Windows/Linux) / `Cmd+I` (macOS): 聚焦输入框

## 📞 支持
- GitHub: https://github.com/Minidoracat/mcp-feedback-enhanced
- 问题反馈: https://github.com/Minidoracat/mcp-feedback-enhanced/issues

## 🎉 安装完成！
您现在可以在 VS Code 中使用 MCP Feedback Enhanced 工具了！
