#!/bin/bash

# MCP Feedback Enhanced 启动脚本
# 使用方法：
#   ./start-mcp.sh web      # 启动 Web UI
#   ./start-mcp.sh desktop  # 启动桌面应用
#   ./start-mcp.sh server   # 启动 MCP 服务器
#   ./start-mcp.sh          # 显示帮助

# 设置 Python 环境
export PYENV_ROOT="$HOME/.pyenv"
export PATH="$PYENV_ROOT/bin:$PATH"
eval "$(pyenv init - bash)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 MCP Feedback Enhanced 启动器${NC}"
echo -e "${BLUE}================================${NC}"

case "$1" in
    "web")
        echo -e "${GREEN}🌐 启动 Web UI 模式...${NC}"
        echo -e "${YELLOW}💡 Web UI 将在浏览器中打开: http://127.0.0.1:9765${NC}"
        echo -e "${YELLOW}💡 按 Ctrl+C 停止服务器${NC}"
        echo ""
        mcp-feedback-enhanced test --web
        ;;
    "desktop")
        echo -e "${GREEN}🖥️ 启动桌面应用模式...${NC}"
        echo -e "${YELLOW}💡 桌面应用将自动启动${NC}"
        echo ""
        mcp-feedback-enhanced test --desktop
        ;;
    "server")
        echo -e "${GREEN}🔧 启动 MCP 服务器模式...${NC}"
        echo -e "${YELLOW}💡 MCP 服务器将等待 AI 工具连接${NC}"
        echo -e "${YELLOW}💡 按 Ctrl+C 停止服务器${NC}"
        echo ""
        mcp-feedback-enhanced server
        ;;
    "version")
        echo -e "${GREEN}📋 版本信息:${NC}"
        mcp-feedback-enhanced version
        ;;
    *)
        echo -e "${YELLOW}📖 使用方法:${NC}"
        echo -e "  ${GREEN}./start-mcp.sh web${NC}      # 启动 Web UI 测试模式"
        echo -e "  ${GREEN}./start-mcp.sh desktop${NC}  # 启动桌面应用测试模式"
        echo -e "  ${GREEN}./start-mcp.sh server${NC}   # 启动 MCP 服务器模式"
        echo -e "  ${GREEN}./start-mcp.sh version${NC}  # 显示版本信息"
        echo ""
        echo -e "${YELLOW}💡 提示:${NC}"
        echo -e "  - ${BLUE}web${NC} 模式：在浏览器中打开交互界面"
        echo -e "  - ${BLUE}desktop${NC} 模式：启动原生桌面应用"
        echo -e "  - ${BLUE}server${NC} 模式：作为 MCP 服务器运行，供 AI 工具连接"
        echo ""
        echo -e "${YELLOW}🔗 配置文件:${NC}"
        echo -e "  - Web UI 配置: ${GREEN}mcp-config.json${NC}"
        echo -e "  - 桌面应用配置: ${GREEN}mcp-config-desktop.json${NC}"
        echo -e "  - 详细说明: ${GREEN}MCP-安装说明.md${NC}"
        ;;
esac
