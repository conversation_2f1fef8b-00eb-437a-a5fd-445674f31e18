{"mcpServers": {"playwright": {"command": "node", "args": ["/Users/<USER>/Desktop/mcp-playwright/dist/index.js"], "env": {"NODE_ENV": "production"}}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["--index-url", "https://mirrors.aliyun.com/pypi/simple/", "mcp-feedback-enhanced@latest"], "timeout": 600, "env": {"MCP_WEB_HOST": "127.0.0.1", "MCP_WEB_PORT": "8765", "MCP_DEBUG": "false", "MCP_LANGUAGE": "zh-CN"}, "autoApprove": ["interactive_feedback"]}}}